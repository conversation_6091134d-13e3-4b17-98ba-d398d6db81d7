using SmaTrendFollower.Models;

namespace SmaTrendFollower.Configuration;

/// <summary>
/// Configuration for flexible data staleness handling
/// </summary>
public sealed class FlexibleStalenessConfiguration
{
    /// <summary>
    /// Market hours staleness thresholds
    /// </summary>
    public MarketHoursStalenessThresholds MarketHours { get; set; } = new();

    /// <summary>
    /// After hours staleness thresholds
    /// </summary>
    public AfterHoursStalenessThresholds AfterHours { get; set; } = new();

    /// <summary>
    /// How long emergency mode stays active by default
    /// </summary>
    public TimeSpan EmergencyModeDuration { get; set; } = TimeSpan.FromMinutes(15);

    /// <summary>
    /// Whether to allow critical operation overrides
    /// </summary>
    public bool AllowCriticalOverrides { get; set; } = true;

    /// <summary>
    /// Whether to allow fallback data usage when primary data is stale
    /// </summary>
    public bool AllowFallbackDataUsage { get; set; } = true;

    /// <summary>
    /// Whether to log staleness warnings
    /// </summary>
    public bool LogStalenessWarnings { get; set; } = true;

    /// <summary>
    /// Gets staleness thresholds for a specific data type and market hours
    /// </summary>
    public StalenessThresholds GetThresholds(DataType dataType, bool isMarketHours)
    {
        var source = isMarketHours ? MarketHours : AfterHours;
        
        return dataType switch
        {
            DataType.HistoricalBars => new StalenessThresholds
            {
                Fresh = source.HistoricalBars * 0.5,
                Acceptable = source.HistoricalBars,
                Stale = source.HistoricalBars * 2.0,
                VeryStale = source.HistoricalBars * 4.0
            },
            DataType.RealTimeQuotes => new StalenessThresholds
            {
                Fresh = source.RealTimeQuotes * 0.5,
                Acceptable = source.RealTimeQuotes,
                Stale = source.RealTimeQuotes * 2.0,
                VeryStale = source.RealTimeQuotes * 4.0
            },
            DataType.IndexData => new StalenessThresholds
            {
                Fresh = source.IndexData * 0.5,
                Acceptable = source.IndexData,
                Stale = source.IndexData * 2.0,
                VeryStale = source.IndexData * 4.0
            },
            DataType.VixData => new StalenessThresholds
            {
                Fresh = source.VixData * 0.5,
                Acceptable = source.VixData,
                Stale = source.VixData * 2.0,
                VeryStale = source.VixData * 4.0
            },
            _ => new StalenessThresholds
            {
                Fresh = TimeSpan.FromMinutes(5),
                Acceptable = TimeSpan.FromMinutes(15),
                Stale = TimeSpan.FromMinutes(30),
                VeryStale = TimeSpan.FromHours(1)
            }
        };
    }
}


