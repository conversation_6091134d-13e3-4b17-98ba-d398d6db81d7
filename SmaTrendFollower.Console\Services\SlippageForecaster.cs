using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.IO.Compression;
using System.Text.Json;
using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// Simple linear regression slippage forecaster that loads weights from a compressed model file
/// </summary>
internal sealed class SlippageForecaster : ISlippageForecaster
{
    private readonly double[] _w;                  // [Intercept, Spread, Vol]
    private readonly SlippageOptions _opt;
    private readonly ILogger<SlippageForecaster> _log;

    public SlippageForecaster(IOptions<SlippageOptions> opt, ILogger<SlippageForecaster> log)
    {
        _opt = opt.Value;
        _log = log;
        _w = LoadWeights(_opt) ?? new[] { _opt.DefaultCents, 0.5, 0.2 };
        
        _log.LogInformation("SlippageForecaster initialized with weights: [{Intercept:F3}, {Spread:F3}, {Vol:F3}]", 
            _w[0], _w[1], _w[2]);
    }

    public double Predict(decimal spread, decimal volPct)
    {
        // Linear regression: slippage = intercept + spread_coeff * spread * 100 + vol_coeff * vol_pct
        var prediction = _w[0] + _w[1] * (double)spread * 100 + _w[2] * (double)volPct;
        
        _log.LogDebug("Slippage prediction: spread={Spread:F4}, vol={Vol:F2}%, predicted={Prediction:F3}¢", 
            spread, volPct, prediction);
            
        return prediction; // cents/share
    }

    private static double[]? LoadWeights(SlippageOptions o)
    {
        try
        {
            if (!File.Exists(o.ModelPath)) 
            {
                return null;
            }
            
            using var fs = File.OpenRead(o.ModelPath);
            using var zip = new ZipArchive(fs, ZipArchiveMode.Read);
            var entry = zip.GetEntry("weights.json") ?? throw new InvalidDataException("weights.json not found in model file");
            
            using var sr = new StreamReader(entry.Open());
            var weightsJson = sr.ReadToEnd();
            var weights = JsonSerializer.Deserialize<double[]>(weightsJson);
            
            if (weights == null || weights.Length != 3)
            {
                throw new InvalidDataException("Invalid weights format - expected array of 3 doubles");
            }
            
            return weights;
        }
        catch (Exception ex)
        {
            // Log the error but don't throw - fall back to default weights
            Console.WriteLine($"Failed to load slippage model from {o.ModelPath}: {ex.Message}");
            return null;
        }
    }
}
