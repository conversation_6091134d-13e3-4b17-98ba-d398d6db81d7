using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using SmaTrendFollower.Models;
using SmaTrendFollower.Services;

namespace SmaTrendFollower.Examples;

/// <summary>
/// Example demonstrating the SlippageForecaster v1 system
/// </summary>
public static class SlippageForecasterExample
{
    public static void RunExample()
    {
        Console.WriteLine("🎯 SlippageForecaster v1 Example");
        Console.WriteLine("================================");

        // Create logger
        using var loggerFactory = LoggerFactory.Create(builder => 
            builder.AddConsole().SetMinimumLevel(LogLevel.Information));
        var logger = loggerFactory.CreateLogger<SlippageForecaster>();

        // Create options
        var options = Options.Create(new SlippageOptions
        {
            ModelPath = "Model/slippage_model.zip",
            DefaultCents = 0.3
        });

        // Create forecaster (will use default weights since no model file exists)
        var forecaster = new SlippageForecaster(options, logger);

        Console.WriteLine("\n📊 Testing Slippage Predictions:");
        Console.WriteLine("--------------------------------");

        // Test various scenarios
        var testCases = new[]
        {
            (spread: 0.01m, vol: 1.5m, description: "Tight spread, low volatility"),
            (spread: 0.05m, vol: 2.5m, description: "Normal spread, moderate volatility"),
            (spread: 0.10m, vol: 4.0m, description: "Wide spread, high volatility"),
            (spread: 0.02m, vol: 0.8m, description: "Small spread, very low volatility"),
            (spread: 0.15m, vol: 6.0m, description: "Very wide spread, extreme volatility")
        };

        foreach (var (spread, vol, description) in testCases)
        {
            var prediction = forecaster.Predict(spread, vol);
            Console.WriteLine($"  {description}:");
            Console.WriteLine($"    Spread: ${spread:F4}, Volatility: {vol:F1}%");
            Console.WriteLine($"    Predicted Slippage: {prediction:F3}¢ per share");
            Console.WriteLine();
        }

        Console.WriteLine("💡 Model Training Example:");
        Console.WriteLine("-------------------------");

        // Create trainer
        var trainer = new SlippageModelTrainer(options, loggerFactory.CreateLogger<SlippageModelTrainer>());

        // Generate some synthetic training data
        var trainingData = GenerateSyntheticTrainingData();
        
        Console.WriteLine($"Generated {trainingData.Count()} synthetic training samples");
        Console.WriteLine("Sample data points:");
        
        foreach (var sample in trainingData.Take(5))
        {
            Console.WriteLine($"  Spread: ${sample.spread:F4}, Vol: {sample.volPct:F1}%, Actual Slippage: {sample.slip:F3}¢");
        }

        // Train the model
        Console.WriteLine("\nTraining model...");
        var success = trainer.Train(trainingData);
        
        if (success)
        {
            Console.WriteLine("✅ Model training completed successfully!");
            Console.WriteLine($"Model saved to: {options.Value.ModelPath}");
        }
        else
        {
            Console.WriteLine("❌ Model training failed");
        }

        Console.WriteLine("\n🎯 SlippageForecaster v1 Example Complete!");
    }

    private static IEnumerable<(double spread, double volPct, double slip)> GenerateSyntheticTrainingData()
    {
        var random = new Random(42); // Fixed seed for reproducible results
        var samples = new List<(double, double, double)>();

        for (int i = 0; i < 500; i++)
        {
            // Generate realistic spread and volatility values
            var spread = 0.005 + random.NextDouble() * 0.15; // $0.005 to $0.155
            var volPct = 0.5 + random.NextDouble() * 8.0;    // 0.5% to 8.5%
            
            // Synthetic slippage model: base + spread_effect + vol_effect + noise
            var baseSlippage = 0.25;
            var spreadEffect = spread * 100 * 0.4; // Spread in cents * coefficient
            var volEffect = volPct * 0.15;         // Volatility effect
            var noise = (random.NextDouble() - 0.5) * 0.2; // Random noise
            
            var actualSlippage = baseSlippage + spreadEffect + volEffect + noise;
            
            // Ensure slippage is positive
            actualSlippage = Math.Max(0.05, actualSlippage);
            
            samples.Add((spread, volPct, actualSlippage));
        }

        return samples;
    }
}
