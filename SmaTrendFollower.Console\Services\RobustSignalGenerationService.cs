using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using SmaTrendFollower.Configuration;
using SmaTrendFollower.Monitoring;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace SmaTrendFollower.Services;

/// <summary>
/// Robust signal generation service with comprehensive error recovery and graceful degradation
/// Implements partial signal generation, symbol-level retry logic, and fallback strategies
/// </summary>
public sealed class RobustSignalGenerationService : IRobustSignalGenerationService
{
    private readonly IAdaptiveSignalGenerator _adaptiveGenerator;
    private readonly ISignalGenerator _fallbackGenerator;
    private readonly IFlexibleDataStalenessService _stalenessService;
    private readonly ILogger<RobustSignalGenerationService> _logger;
    private readonly RobustSignalConfiguration _config;
    private readonly ConcurrentDictionary<string, SymbolErrorHistory> _errorHistory = new();
    private readonly SemaphoreSlim _generationSemaphore;

    public RobustSignalGenerationService(
        IAdaptiveSignalGenerator adaptiveGenerator,
        ISignalGenerator fallbackGenerator,
        IFlexibleDataStalenessService stalenessService,
        ILogger<RobustSignalGenerationService> logger,
        RobustSignalConfiguration config)
    {
        _adaptiveGenerator = adaptiveGenerator;
        _fallbackGenerator = fallbackGenerator;
        _stalenessService = stalenessService;
        _logger = logger;
        _config = config;
        _generationSemaphore = new SemaphoreSlim(_config.MaxConcurrentGenerations, _config.MaxConcurrentGenerations);
    }

    /// <summary>
    /// Generates signals with comprehensive error recovery and partial success handling
    /// </summary>
    public async Task<RobustSignalResult> GenerateSignalsRobustlyAsync(
        int topN = 10,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var result = new RobustSignalResult
        {
            RequestedCount = topN,
            StartTime = DateTime.UtcNow
        };

        _logger.LogInformation("Starting robust signal generation for {TopN} signals", topN);

        try
        {
            // Phase 1: Try adaptive signal generation
            var adaptiveResult = await TryAdaptiveGenerationAsync(topN, result, cancellationToken);
            
            // Phase 2: Fill gaps with fallback generation if needed
            if (adaptiveResult.Signals.Count < topN && _config.EnableFallbackGeneration)
            {
                await TryFallbackGenerationAsync(topN - adaptiveResult.Signals.Count, result, cancellationToken);
            }

            // Phase 3: Emergency signal generation if still insufficient
            if (result.Signals.Count < _config.MinimumAcceptableSignals && _config.EnableEmergencyGeneration)
            {
                await TryEmergencyGenerationAsync(result, cancellationToken);
            }

            // Finalize result
            result.EndTime = DateTime.UtcNow;
            result.TotalDuration = stopwatch.Elapsed;
            result.IsSuccess = result.Signals.Count >= _config.MinimumAcceptableSignals;
            result.SuccessRate = result.AttemptedSymbols > 0 
                ? (double)result.SuccessfulSymbols / result.AttemptedSymbols 
                : 0.0;

            _logger.LogInformation("Robust signal generation completed: {Generated}/{Requested} signals, " +
                                 "{Success:P2} success rate, {Duration:F1}s",
                result.Signals.Count, topN, result.SuccessRate, stopwatch.Elapsed.TotalSeconds);

            // Record metrics
            RecordGenerationMetrics(result);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Critical error in robust signal generation");
            result.EndTime = DateTime.UtcNow;
            result.TotalDuration = stopwatch.Elapsed;
            result.IsSuccess = false;
            result.ErrorMessage = ex.Message;
            
            MetricsRegistry.ApplicationErrors.WithLabels("RobustSignalGeneration", "Critical").Inc();
            return result;
        }
    }

    private async Task<AdaptiveGenerationResult> TryAdaptiveGenerationAsync(
        int topN,
        RobustSignalResult result,
        CancellationToken cancellationToken)
    {
        var adaptiveResult = new AdaptiveGenerationResult();
        
        try
        {
            _logger.LogDebug("Attempting adaptive signal generation");
            
            using var timeoutCts = new CancellationTokenSource(_config.AdaptiveGenerationTimeout);
            using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, timeoutCts.Token);

            var signals = await _adaptiveGenerator.GenerateSignalsAsync(topN * 2, combinedCts.Token); // Request more to allow filtering
            
            var validSignals = signals
                .Where(s => s.IsValid && s.ConfidenceScore >= _config.MinimumConfidenceScore)
                .OrderByDescending(s => s.ConfidenceScore)
                .Take(topN)
                .ToList();

            adaptiveResult.Signals.AddRange(validSignals);
            result.Signals.AddRange(validSignals.Select(s => s.ToTradingSignal()));
            result.AttemptedSymbols += signals.Count();
            result.SuccessfulSymbols += validSignals.Count;
            result.GenerationMethods.Add("Adaptive");

            _logger.LogInformation("Adaptive generation produced {Count} valid signals", validSignals.Count);
        }
        catch (OperationCanceledException) when (cancellationToken.IsCancellationRequested)
        {
            _logger.LogWarning("Adaptive signal generation cancelled by user");
            result.ErrorMessage = "Generation cancelled";
        }
        catch (OperationCanceledException)
        {
            _logger.LogWarning("Adaptive signal generation timed out after {Timeout}", _config.AdaptiveGenerationTimeout);
            result.ErrorMessage = "Adaptive generation timeout";
            result.Warnings.Add("Adaptive generation timed out");
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Adaptive signal generation failed, will try fallback");
            result.ErrorMessage = $"Adaptive generation failed: {ex.Message}";
            result.Warnings.Add($"Adaptive generation error: {ex.Message}");
        }

        return adaptiveResult;
    }

    private async Task TryFallbackGenerationAsync(
        int remainingCount,
        RobustSignalResult result,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Attempting fallback signal generation for {Count} additional signals", remainingCount);

            // Activate relaxed staleness policy for fallback generation
            await _stalenessService.SetPolicyAsync(StalenessPolicy.Relaxed, "Fallback signal generation");

            using var timeoutCts = new CancellationTokenSource(_config.FallbackGenerationTimeout);
            using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, timeoutCts.Token);

            var fallbackSignals = await _fallbackGenerator.RunAsync(remainingCount * 2, combinedCts.Token);
            
            var validFallbackSignals = fallbackSignals
                .Where(s => !result.Signals.Any(existing => existing.Symbol == s.Symbol)) // Avoid duplicates
                .Take(remainingCount)
                .ToList();

            result.Signals.AddRange(validFallbackSignals);
            result.AttemptedSymbols += fallbackSignals.Count();
            result.SuccessfulSymbols += validFallbackSignals.Count;
            result.GenerationMethods.Add("Fallback");

            _logger.LogInformation("Fallback generation produced {Count} additional signals", validFallbackSignals.Count);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Fallback signal generation failed");
            result.Warnings.Add($"Fallback generation error: {ex.Message}");
        }
        finally
        {
            // Restore standard staleness policy
            await _stalenessService.SetPolicyAsync(StalenessPolicy.Standard, "Fallback generation completed");
        }
    }

    private async Task TryEmergencyGenerationAsync(
        RobustSignalResult result,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogWarning("Attempting emergency signal generation - activating emergency mode");

            // Activate emergency mode for maximum data tolerance
            await _stalenessService.ActivateEmergencyModeAsync("Emergency signal generation", _config.EmergencyModeDuration);

            // Generate signals for core symbols (SPY, QQQ, etc.)
            var coreSymbols = _config.CoreSymbols;
            var emergencySignals = new List<TradingSignal>();

            foreach (var symbol in coreSymbols)
            {
                if (cancellationToken.IsCancellationRequested)
                    break;

                try
                {
                    var signal = await GenerateEmergencySignalAsync(symbol, cancellationToken);
                    if (signal != null && !result.Signals.Any(s => s.Symbol == symbol))
                    {
                        emergencySignals.Add(signal);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to generate emergency signal for {Symbol}", symbol);
                }
            }

            result.Signals.AddRange(emergencySignals);
            result.AttemptedSymbols += coreSymbols.Count;
            result.SuccessfulSymbols += emergencySignals.Count;
            result.GenerationMethods.Add("Emergency");

            _logger.LogWarning("Emergency generation produced {Count} signals from core symbols", emergencySignals.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Emergency signal generation failed");
            result.Warnings.Add($"Emergency generation error: {ex.Message}");
        }
    }

    private async Task<TradingSignal?> GenerateEmergencySignalAsync(string symbol, CancellationToken cancellationToken)
    {
        try
        {
            // Use very basic signal generation with minimal requirements
            var signals = await _fallbackGenerator.GenerateSignalsAsync(1, cancellationToken);
            return signals.FirstOrDefault(s => s.Symbol == symbol);
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Emergency signal generation failed for {Symbol}", symbol);
            return null;
        }
    }

    /// <summary>
    /// Gets error statistics for symbols
    /// </summary>
    public SymbolErrorStatistics GetErrorStatistics()
    {
        var stats = new SymbolErrorStatistics
        {
            TotalSymbolsTracked = _errorHistory.Count,
            SymbolsWithErrors = _errorHistory.Values.Count(h => h.ErrorCount > 0),
            TotalErrors = _errorHistory.Values.Sum(h => h.ErrorCount),
            AverageErrorsPerSymbol = _errorHistory.Count > 0 
                ? _errorHistory.Values.Average(h => h.ErrorCount) 
                : 0.0
        };

        stats.TopErrorSymbols = _errorHistory
            .Where(kvp => kvp.Value.ErrorCount > 0)
            .OrderByDescending(kvp => kvp.Value.ErrorCount)
            .Take(10)
            .ToDictionary(kvp => kvp.Key, kvp => kvp.Value.ErrorCount);

        return stats;
    }

    /// <summary>
    /// Clears error history for a symbol
    /// </summary>
    public void ClearErrorHistory(string symbol)
    {
        _errorHistory.TryRemove(symbol, out _);
        _logger.LogInformation("Cleared error history for {Symbol}", symbol);
    }

    /// <summary>
    /// Clears all error history
    /// </summary>
    public void ClearAllErrorHistory()
    {
        var count = _errorHistory.Count;
        _errorHistory.Clear();
        _logger.LogInformation("Cleared error history for {Count} symbols", count);
    }

    private void RecordSymbolError(string symbol, Exception ex)
    {
        var history = _errorHistory.GetOrAdd(symbol, _ => new SymbolErrorHistory());
        history.RecordError(ex);
        
        _logger.LogDebug("Recorded error for {Symbol}: {Error} (total: {Count})", 
            symbol, ex.Message, history.ErrorCount);
    }

    private bool ShouldSkipSymbol(string symbol)
    {
        if (!_errorHistory.TryGetValue(symbol, out var history))
            return false;

        return history.ShouldSkip(_config.MaxErrorsPerSymbol, _config.ErrorCooldownPeriod);
    }

    private static void RecordGenerationMetrics(RobustSignalResult result)
    {
        MetricsRegistry.SignalGenerationDuration
            .WithLabels("robust")
            .Observe(result.TotalDuration.TotalSeconds);

        MetricsRegistry.SignalGenerationSuccess
            .WithLabels("robust", result.IsSuccess.ToString())
            .Inc();

        MetricsRegistry.SignalGenerationCount
            .WithLabels("robust")
            .Set(result.Signals.Count);

        foreach (var method in result.GenerationMethods)
        {
            MetricsRegistry.SignalGenerationMethods
                .WithLabels(method)
                .Inc();
        }
    }

    public void Dispose()
    {
        _generationSemaphore?.Dispose();
    }
}

/// <summary>
/// Interface for robust signal generation service
/// </summary>
public interface IRobustSignalGenerationService : IDisposable
{
    Task<RobustSignalResult> GenerateSignalsRobustlyAsync(int topN = 10, CancellationToken cancellationToken = default);
    SymbolErrorStatistics GetErrorStatistics();
    void ClearErrorHistory(string symbol);
    void ClearAllErrorHistory();
}

/// <summary>
/// Result of adaptive signal generation attempt
/// </summary>
internal sealed class AdaptiveGenerationResult
{
    public List<AdaptiveTradingSignal> Signals { get; } = new();
}
