using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// Simple slippage forecaster interface for predicting per-trade slippage in cents per share
/// </summary>
public interface ISlippageForecaster
{
    /// <summary>
    /// Predicted slippage in *cents* per share.
    /// </summary>
    /// <param name="bidAskSpread">Bid-ask spread in dollars</param>
    /// <param name="intradayVolPct">Intraday volatility percentage</param>
    /// <returns>Predicted slippage in cents per share</returns>
    double Predict(decimal bidAskSpread, decimal intradayVolPct);

    /// <summary>
    /// Predicted slippage in basis points using enhanced features
    /// </summary>
    /// <param name="features">Slippage signal features</param>
    /// <param name="quote">Quote context</param>
    /// <returns>Predicted slippage in basis points</returns>
    float PredictBps(SlippageSignalFeatures features, QuoteContext quote);

    /// <summary>
    /// Indicates whether the forecaster is ready to make predictions
    /// </summary>
    bool IsReady { get; }
}
