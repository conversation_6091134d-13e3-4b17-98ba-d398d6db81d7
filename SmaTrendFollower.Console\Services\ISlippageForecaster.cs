namespace SmaTrendFollower.Services;

/// <summary>
/// Simple slippage forecaster interface for predicting per-trade slippage in cents per share
/// </summary>
public interface ISlippageForecaster
{
    /// <summary>
    /// Predicted slippage in *cents* per share.
    /// </summary>
    /// <param name="bidAskSpread">Bid-ask spread in dollars</param>
    /// <param name="intradayVolPct">Intraday volatility percentage</param>
    /// <returns>Predicted slippage in cents per share</returns>
    double Predict(decimal bidAskSpread, decimal intradayVolPct);
}
