using Alpaca.Markets;

namespace SmaTrendFollower.Models;

/// <summary>
/// Synthetic bar implementation for generated market data
/// </summary>
public sealed class SyntheticBar : IBar
{
    public string Symbol { get; set; } = string.Empty;
    public DateTime TimeUtc { get; set; }
    public decimal Open { get; set; }
    public decimal High { get; set; }
    public decimal Low { get; set; }
    public decimal Close { get; set; }
    public ulong Volume { get; set; }
    public decimal Vwap { get; set; }
    public ulong TradeCount { get; set; }
}

/// <summary>
/// Page wrapper for synthetic bars
/// </summary>
public sealed class SyntheticBarPage : IPage<IBar>
{
    private readonly List<IBar> _items;

    public SyntheticBarPage(List<IBar> items, string symbol)
    {
        _items = items;
        Symbol = symbol;
    }

    public IReadOnlyList<IBar> Items => _items;
    public string? NextPageToken => null; // Synthetic data doesn't support pagination
    public string Symbol { get; }
}


