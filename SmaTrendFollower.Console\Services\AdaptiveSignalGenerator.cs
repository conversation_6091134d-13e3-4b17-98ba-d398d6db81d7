using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using SmaTrendFollower.Configuration;
using SmaTrendFollower.Monitoring;
using SmaTrendFollower.Extensions;
using Skender.Stock.Indicators;
using System.Diagnostics;
using Alpaca.Markets;

namespace SmaTrendFollower.Services;

/// <summary>
/// Adaptive signal generator that works with limited historical data
/// Uses alternative indicators and adaptive calculations for stocks with insufficient history
/// </summary>
public sealed class AdaptiveSignalGenerator : IAdaptiveSignalGenerator
{
    private readonly IEnhancedDataRetrievalService _dataRetrievalService;
    private readonly IUniverseProvider _universeProvider;
    private readonly ILogger<AdaptiveSignalGenerator> _logger;
    private readonly AdaptiveSignalConfiguration _config;
    private readonly SemaphoreSlim _rateLimitSemaphore;

    public AdaptiveSignalGenerator(
        IEnhancedDataRetrievalService dataRetrievalService,
        IUniverseProvider universeProvider,
        ILogger<AdaptiveSignalGenerator> logger,
        AdaptiveSignalConfiguration config)
    {
        _dataRetrievalService = dataRetrievalService;
        _universeProvider = universeProvider;
        _logger = logger;
        _config = config;
        _rateLimitSemaphore = new SemaphoreSlim(_config.MaxConcurrentSymbols, _config.MaxConcurrentSymbols);
    }

    /// <summary>
    /// Generates trading signals with adaptive requirements based on available data
    /// </summary>
    public async Task<IEnumerable<AdaptiveTradingSignal>> GenerateSignalsAsync(
        int topN = 10,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        _logger.LogInformation("Starting adaptive signal generation for top {TopN} signals", topN);

        try
        {
            // Get universe of symbols
            var symbols = await _universeProvider.GetUniverseAsync();
            var symbolList = symbols.Take(_config.MaxSymbolsToProcess).ToList();

            _logger.LogInformation("Processing {Count} symbols for adaptive signal generation", symbolList.Count);

            // Process symbols in parallel with rate limiting
            var signals = new List<AdaptiveTradingSignal>();
            var tasks = symbolList.Select(async symbol =>
            {
                await _rateLimitSemaphore.WaitAsync(cancellationToken);
                try
                {
                    var signal = await ProcessSymbolAdaptivelyAsync(symbol, cancellationToken);
                    if (signal != null)
                    {
                        lock (signals)
                        {
                            signals.Add(signal);
                        }
                    }
                }
                finally
                {
                    _rateLimitSemaphore.Release();
                }
            });

            await Task.WhenAll(tasks);

            // Filter and rank signals
            var validSignals = signals
                .Where(s => s.IsValid)
                .OrderByDescending(s => s.ConfidenceScore)
                .ThenByDescending(s => s.AdaptedReturn)
                .Take(topN)
                .ToList();

            stopwatch.Stop();
            _logger.LogInformation("Adaptive signal generation completed: {Valid}/{Total} valid signals in {Duration:F1}s",
                validSignals.Count, signals.Count, stopwatch.Elapsed.TotalSeconds);

            // Record metrics
            MetricsRegistry.SignalLatencyMs.Observe(stopwatch.Elapsed.TotalMilliseconds);
            MetricsRegistry.SignalsTotal.WithLabels("adaptive", "true").Set(validSignals.Count);

            return validSignals;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in adaptive signal generation");
            MetricsRegistry.ApplicationErrors.WithLabels("AdaptiveSignalGenerator", "GenerateSignalsAsync").Inc();
            return Enumerable.Empty<AdaptiveTradingSignal>();
        }
    }

    private async Task<AdaptiveTradingSignal?> ProcessSymbolAdaptivelyAsync(
        string symbol,
        CancellationToken cancellationToken)
    {
        try
        {
            // Get historical data with fallback strategies
            var endDate = DateTime.UtcNow.Date.AddDays(-1);
            var startDate = endDate.AddDays(-_config.MaxLookbackDays);

            var dataResult = await _dataRetrievalService.GetStockBarsAsync(symbol, startDate, endDate, cancellationToken);
            
            if (!dataResult.IsSuccess || dataResult.Data == null)
            {
                _logger.LogDebug("Failed to retrieve data for {Symbol}: {Error}", symbol, dataResult.ErrorMessage);
                return null;
            }

            var bars = dataResult.Data.Items.ToList();
            if (bars.Count < _config.MinimumBarsRequired)
            {
                _logger.LogDebug("Insufficient data for {Symbol}: {Count} bars (minimum: {Min})",
                    symbol, bars.Count, _config.MinimumBarsRequired);
                return null;
            }

            // Determine the best signal strategy based on available data
            var strategy = DetermineOptimalStrategy(bars.Count);
            var signal = await GenerateSignalWithStrategyAsync(symbol, bars, strategy, dataResult.DataQuality);

            if (signal != null)
            {
                signal.DataSource = dataResult.FinalDataSource.ToString();
                signal.DataQuality = dataResult.DataQuality;
                signal.Strategy = strategy;
                
                _logger.LogDebug("Generated adaptive signal for {Symbol}: Strategy={Strategy}, Confidence={Confidence:F2}",
                    symbol, strategy, signal.ConfidenceScore);
            }

            return signal;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error processing symbol {Symbol} adaptively", symbol);
            return null;
        }
    }

    private SignalStrategy DetermineOptimalStrategy(int availableBars)
    {
        if (availableBars >= 200)
            return SignalStrategy.FullSMA; // Traditional 200-day SMA
        if (availableBars >= 100)
            return SignalStrategy.AdaptedSMA; // Shorter SMA periods
        if (availableBars >= 50)
            return SignalStrategy.MomentumBased; // Momentum and RSI
        if (availableBars >= 20)
            return SignalStrategy.ShortTermTrend; // Short-term trend analysis
        
        return SignalStrategy.PriceAction; // Basic price action
    }

    private async Task<AdaptiveTradingSignal?> GenerateSignalWithStrategyAsync(
        string symbol,
        List<IBar> bars,
        SignalStrategy strategy,
        DataQuality dataQuality)
    {
        try
        {
            return strategy switch
            {
                SignalStrategy.FullSMA => await GenerateFullSMASignalAsync(symbol, bars, dataQuality),
                SignalStrategy.AdaptedSMA => await GenerateAdaptedSMASignalAsync(symbol, bars, dataQuality),
                SignalStrategy.MomentumBased => await GenerateMomentumSignalAsync(symbol, bars, dataQuality),
                SignalStrategy.ShortTermTrend => await GenerateShortTermTrendSignalAsync(symbol, bars, dataQuality),
                SignalStrategy.PriceAction => await GeneratePriceActionSignalAsync(symbol, bars, dataQuality),
                _ => null
            };
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error generating {Strategy} signal for {Symbol}", strategy, symbol);
            return null;
        }
    }

    private async Task<AdaptiveTradingSignal?> GenerateFullSMASignalAsync(
        string symbol,
        List<IBar> bars,
        DataQuality dataQuality)
    {
        // Traditional SMA200 + SMA50 strategy
        var quotes = bars.ToQuotes();
        var sma200 = quotes.GetSma(200).LastOrDefault()?.Sma;
        var sma50 = quotes.GetSma(50).LastOrDefault()?.Sma;
        var currentPrice = bars.Last().Close;

        if (!sma200.HasValue || !sma50.HasValue)
            return null;

        var isUptrend = currentPrice > (decimal)sma200.Value && currentPrice > (decimal)sma50.Value;
        if (!isUptrend)
            return null;

        var rsi = quotes.GetRsi(14).LastOrDefault()?.Rsi ?? 50;
        var atr = quotes.GetAtr(14).LastOrDefault()?.Atr ?? 0;

        return new AdaptiveTradingSignal
        {
            Symbol = symbol,
            Price = currentPrice,
            IsValid = true,
            ConfidenceScore = CalculateConfidenceScore(dataQuality, 0.9, rsi),
            AdaptedReturn = CalculateAdaptedReturn(bars, 126), // 6-month return
            Atr = (decimal)atr,
            Rsi = rsi,
            ValidationTimestamp = DateTime.UtcNow
        };
    }

    private async Task<AdaptiveTradingSignal?> GenerateAdaptedSMASignalAsync(
        string symbol,
        List<IBar> bars,
        DataQuality dataQuality)
    {
        // Adapted SMA strategy with shorter periods
        var availableBars = bars.Count;
        var longPeriod = Math.Min(100, availableBars - 10);
        var shortPeriod = Math.Min(20, longPeriod / 2);

        var quotes = bars.ToQuotes();
        var smaLong = quotes.GetSma(longPeriod).LastOrDefault()?.Sma;
        var smaShort = quotes.GetSma(shortPeriod).LastOrDefault()?.Sma;
        var currentPrice = bars.Last().Close;

        if (!smaLong.HasValue || !smaShort.HasValue)
            return null;

        var isUptrend = currentPrice > (decimal)smaLong.Value && currentPrice > (decimal)smaShort.Value;
        if (!isUptrend)
            return null;

        var rsi = quotes.GetRsi(14).LastOrDefault()?.Rsi ?? 50;
        var atr = quotes.GetAtr(14).LastOrDefault()?.Atr ?? 0;

        return new AdaptiveTradingSignal
        {
            Symbol = symbol,
            Price = currentPrice,
            IsValid = true,
            ConfidenceScore = CalculateConfidenceScore(dataQuality, 0.8, rsi),
            AdaptedReturn = CalculateAdaptedReturn(bars, Math.Min(63, availableBars - 5)), // Adapted return period
            Atr = (decimal)atr,
            Rsi = rsi,
            ValidationTimestamp = DateTime.UtcNow
        };
    }

    private async Task<AdaptiveTradingSignal?> GenerateMomentumSignalAsync(
        string symbol,
        List<IBar> bars,
        DataQuality dataQuality)
    {
        // Momentum-based strategy using RSI and price momentum
        var currentPrice = bars.Last().Close;
        var quotes = bars.ToQuotes();
        var rsi = quotes.GetRsi(14).LastOrDefault()?.Rsi ?? 50;
        
        // Calculate price momentum over available period
        var momentumPeriod = Math.Min(20, bars.Count - 5);
        var oldPrice = bars[bars.Count - momentumPeriod - 1].Close;
        var momentum = (currentPrice - oldPrice) / oldPrice;

        // Require positive momentum and RSI not overbought
        if (momentum <= 0 || rsi > 70)
            return null;

        var atr = quotes.GetAtr(Math.Min(14, bars.Count - 1)).LastOrDefault()?.Atr ?? 0;

        return new AdaptiveTradingSignal
        {
            Symbol = symbol,
            Price = currentPrice,
            IsValid = true,
            ConfidenceScore = CalculateConfidenceScore(dataQuality, 0.7, rsi),
            AdaptedReturn = momentum,
            Atr = (decimal)atr,
            Rsi = rsi,
            ValidationTimestamp = DateTime.UtcNow
        };
    }

    private async Task<AdaptiveTradingSignal?> GenerateShortTermTrendSignalAsync(
        string symbol,
        List<IBar> bars,
        DataQuality dataQuality)
    {
        // Short-term trend analysis
        var currentPrice = bars.Last().Close;
        var availableBars = bars.Count;
        
        // Use available data for trend calculation
        var trendPeriod = Math.Min(10, availableBars - 2);
        var quotes = bars.ToQuotes();
        var sma = quotes.GetSma(trendPeriod).LastOrDefault()?.Sma;
        
        if (!sma.HasValue || currentPrice <= (decimal)sma.Value)
            return null;

        // Calculate short-term return
        var returnPeriod = Math.Min(5, availableBars - 1);
        var oldPrice = bars[bars.Count - returnPeriod - 1].Close;
        var shortTermReturn = (currentPrice - oldPrice) / oldPrice;

        var atr = quotes.GetAtr(Math.Min(10, bars.Count - 1)).LastOrDefault()?.Atr ?? 0;

        return new AdaptiveTradingSignal
        {
            Symbol = symbol,
            Price = currentPrice,
            IsValid = true,
            ConfidenceScore = CalculateConfidenceScore(dataQuality, 0.6, 50), // Neutral RSI assumption
            AdaptedReturn = shortTermReturn,
            Atr = (decimal)atr,
            Rsi = 50, // Default RSI
            ValidationTimestamp = DateTime.UtcNow
        };
    }

    private async Task<AdaptiveTradingSignal?> GeneratePriceActionSignalAsync(
        string symbol,
        List<IBar> bars,
        DataQuality dataQuality)
    {
        // Basic price action strategy
        var currentPrice = bars.Last().Close;
        var availableBars = bars.Count;
        
        // Simple uptrend check - current price higher than average of available bars
        var avgPrice = bars.Average(b => b.Close);
        if (currentPrice <= avgPrice)
            return null;

        // Calculate return over available period
        var firstPrice = bars.First().Close;
        var totalReturn = (currentPrice - firstPrice) / firstPrice;

        // Estimate ATR from high-low ranges
        var avgRange = bars.Average(b => b.High - b.Low);

        return new AdaptiveTradingSignal
        {
            Symbol = symbol,
            Price = currentPrice,
            IsValid = true,
            ConfidenceScore = CalculateConfidenceScore(dataQuality, 0.5, 50), // Lower confidence for basic strategy
            AdaptedReturn = totalReturn,
            Atr = avgRange,
            Rsi = 50, // Default RSI
            ValidationTimestamp = DateTime.UtcNow
        };
    }

    private static double CalculateConfidenceScore(DataQuality dataQuality, double baseConfidence, double rsi)
    {
        // Adjust confidence based on data quality
        var qualityMultiplier = dataQuality switch
        {
            DataQuality.High => 1.0,
            DataQuality.Medium => 0.9,
            DataQuality.Low => 0.8,
            DataQuality.Synthetic => 0.7,
            DataQuality.Emergency => 0.6,
            _ => 0.5
        };

        // Adjust for RSI (prefer RSI between 30-70)
        var rsiMultiplier = rsi switch
        {
            >= 30 and <= 70 => 1.0,
            >= 20 and < 30 => 0.9,
            > 70 and <= 80 => 0.9,
            _ => 0.8
        };

        return baseConfidence * qualityMultiplier * rsiMultiplier;
    }

    private static decimal CalculateAdaptedReturn(List<IBar> bars, int lookbackDays)
    {
        if (bars.Count <= lookbackDays)
        {
            // Use all available data
            var firstPrice = bars.First().Close;
            var lastPrice = bars.Last().Close;
            return (lastPrice - firstPrice) / firstPrice;
        }

        // Use specified lookback period
        var startPrice = bars[bars.Count - lookbackDays - 1].Close;
        var endPrice = bars.Last().Close;
        return (endPrice - startPrice) / startPrice;
    }
}

/// <summary>
/// Interface for adaptive signal generation
/// </summary>
public interface IAdaptiveSignalGenerator
{
    Task<IEnumerable<AdaptiveTradingSignal>> GenerateSignalsAsync(int topN = 10, CancellationToken cancellationToken = default);
}

/// <summary>
/// Signal generation strategies based on available data
/// </summary>
public enum SignalStrategy
{
    FullSMA,        // Traditional 200-day SMA strategy
    AdaptedSMA,     // Shorter SMA periods
    MomentumBased,  // Momentum and RSI based
    ShortTermTrend, // Short-term trend analysis
    PriceAction     // Basic price action
}
